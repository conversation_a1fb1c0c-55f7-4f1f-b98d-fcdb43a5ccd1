/* Reset và base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Main content */
main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }
}

/* Camera section */
.camera-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.video-container {
    position: relative;
    width: 100%;
    max-width: 640px;
    margin: 0 auto 20px;
    border-radius: 10px;
    overflow: hidden;
    background: #f0f0f0;
}

#video {
    width: 100%;
    height: auto;
    display: block;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    border-radius: 10px;
}

/* Controls */
.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-success {
    background: #2196F3;
    color: white;
}

.btn-secondary {
    background: #FF9800;
    color: white;
}

.btn-danger {
    background: #f44336;
    color: white;
}

/* Filters */
.filters, .frames {
    margin-bottom: 20px;
}

.filters h3, .frames h3 {
    margin-bottom: 10px;
    color: #555;
}

.filter-buttons, .frame-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.filter-btn, .frame-btn {
    padding: 8px 16px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-btn:hover, .frame-btn:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.filter-btn.active, .frame-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Gallery section */
.gallery-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.gallery-section h3 {
    margin-bottom: 15px;
    color: #555;
}

.gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.gallery-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.gallery-item .delete-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(244, 67, 54, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Footer */
footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    margin-top: 30px;
}

/* Filter effects */
.filter-none { filter: none; }
.filter-sepia { filter: sepia(100%); }
.filter-grayscale { filter: grayscale(100%); }
.filter-blur { filter: blur(2px); }
.filter-brightness { filter: brightness(150%); }
.filter-contrast { filter: contrast(150%); }
.filter-hue-rotate { filter: hue-rotate(90deg); }

/* Frame effects */
.frame-none { border: none; }
.frame-classic { border: 10px solid #8B4513; }
.frame-modern { border: 5px solid #333; }
.frame-fun { border: 8px solid #FF69B4; border-radius: 20px; }
.frame-vintage { border: 12px solid #D2691E; box-shadow: inset 0 0 20px rgba(0,0,0,0.3); }

/* Responsive */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
    }
}
