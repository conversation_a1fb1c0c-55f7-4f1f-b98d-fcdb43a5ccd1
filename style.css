/* Reset và base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #b2b6c9 0%, #764ba2 100%); 
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Main content */
main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }
}

/* Camera section */
.camera-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.video-container {
    position: relative;
    width: 100%;
    max-width: 640px;
    margin: 0 auto 20px;
    border-radius: 10px;
    overflow: hidden;
    background: #f0f0f0;
}

#video {
    width: 100%;
    height: auto;
    display: block;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    border-radius: 10px;
}

/* Controls */
.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-success {
    background: #2196F3;
    color: white;
}

.btn-secondary {
    background: #FF9800;
    color: white;
}

.btn-danger {
    background: #f44336;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Filters, Frames, Stickers, Text, Beauty */
.filters, .frames, .stickers, .text-overlay, .beauty-filters {
    margin-bottom: 20px;
}

.filters h3, .frames h3, .stickers h3, .text-overlay h3, .beauty-filters h3 {
    margin-bottom: 10px;
    color: #555;
}

.filter-buttons, .frame-buttons, .sticker-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.filter-btn, .frame-btn, .sticker-btn {
    padding: 8px 16px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-btn:hover, .frame-btn:hover, .sticker-btn:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.filter-btn.active, .frame-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.sticker-btn {
    font-size: 1.5rem;
    padding: 8px 12px;
    min-width: 50px;
}

.sticker-btn.selected {
    background: #ffeb3b;
    border-color: #fbc02d;
}

/* Text overlay controls */
.text-overlay input, .text-overlay select {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    margin: 5px;
    font-size: 0.9rem;
}

.text-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: center;
    margin-top: 10px;
}

/* Overlay elements */
.overlay-element {
    position: absolute;
    cursor: move;
    user-select: none;
    z-index: 10;
}

.overlay-sticker {
    font-size: 40px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.overlay-text {
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    padding: 5px 10px;
    border-radius: 5px;
    background: rgba(0,0,0,0.3);
}

.overlay-element:hover {
    transform: scale(1.1);
}

.overlay-element .delete-overlay {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: none;
}

.overlay-element:hover .delete-overlay {
    display: block;
}

/* Beauty Filters */
.beauty-filters {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.beauty-filters h3 {
    color: #495057;
    margin-bottom: 15px;
    text-align: center;
}

.beauty-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.beauty-control {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.beauty-control label {
    min-width: 100px;
    font-size: 0.9rem;
    font-weight: 500;
    color: #495057;
}

.beauty-slider {
    flex: 1;
    min-width: 120px;
    height: 6px;
    border-radius: 3px;
    background: #dee2e6;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.beauty-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.beauty-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.beauty-slider:hover::-webkit-slider-thumb {
    background: #5a67d8;
    transform: scale(1.1);
}

.beauty-slider:hover::-moz-range-thumb {
    background: #5a67d8;
    transform: scale(1.1);
}

.beauty-value {
    min-width: 40px;
    text-align: center;
    font-size: 0.8rem;
    font-weight: bold;
    color: #667eea;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.beauty-controls .btn {
    margin: 5px;
    min-width: 80px;
}

/* Beauty filter effects for video preview */
.beauty-smooth-light { filter: blur(0.5px) brightness(1.05); }
.beauty-smooth-medium { filter: blur(1px) brightness(1.1); }
.beauty-smooth-heavy { filter: blur(1.5px) brightness(1.15); }

.beauty-brighten-light { filter: brightness(1.1) contrast(0.95); }
.beauty-brighten-medium { filter: brightness(1.2) contrast(0.9); }
.beauty-brighten-heavy { filter: brightness(1.3) contrast(0.85); }

.beauty-tone-light { filter: hue-rotate(-5deg) saturate(1.1) brightness(1.05); }
.beauty-tone-medium { filter: hue-rotate(-10deg) saturate(1.2) brightness(1.1); }
.beauty-tone-heavy { filter: hue-rotate(-15deg) saturate(1.3) brightness(1.15); }

.beauty-blemish-light { filter: blur(0.3px) contrast(1.05); }
.beauty-blemish-medium { filter: blur(0.6px) contrast(1.1); }
.beauty-blemish-heavy { filter: blur(1px) contrast(1.15); }

/* Gallery section */
.gallery-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.gallery-section h3 {
    margin-bottom: 15px;
    color: #555;
}

.gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.gallery-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.gallery-item .delete-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(244, 67, 54, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-item .media-type {
    position: absolute;
    bottom: 5px;
    left: 5px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
}

.gallery-item video {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.gallery-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Share Modal */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}

.share-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 20px;
}

.share-btn {
    padding: 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: transform 0.2s ease;
}

.share-btn:hover {
    transform: translateY(-2px);
}

.share-btn[data-platform="facebook"] { background: #1877f2; color: white; }
.share-btn[data-platform="twitter"] { background: #1da1f2; color: white; }
.share-btn[data-platform="instagram"] { background: #e4405f; color: white; }
.share-btn[data-platform="whatsapp"] { background: #25d366; color: white; }
.share-btn[data-platform="email"] { background: #34495e; color: white; }
.share-btn[data-platform="copy"] { background: #95a5a6; color: white; }

/* Recording indicator */
.recording-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #f44336;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Burst mode countdown */
.burst-countdown {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 4rem;
    font-weight: bold;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    z-index: 100;
}

/* Footer */
footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    margin-top: 30px;
}

/* Filter effects */
.filter-none { filter: none; }
.filter-sepia { filter: sepia(100%); }
.filter-grayscale { filter: grayscale(100%); }
.filter-blur { filter: blur(2px); }
.filter-brightness { filter: brightness(150%); }
.filter-contrast { filter: contrast(150%); }
.filter-hue-rotate { filter: hue-rotate(90deg); }

/* Frame effects */
.frame-none { border: none; }
.frame-classic { border: 10px solid #8B4513; }
.frame-modern { border: 5px solid #333; }
.frame-fun { border: 8px solid #FF69B4; border-radius: 20px; }
.frame-vintage { border: 12px solid #D2691E; box-shadow: inset 0 0 20px rgba(0,0,0,0.3); }

/* Responsive */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
    }
}
