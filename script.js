class Photobooth {
    constructor() {
        this.video = document.getElementById('video');
        this.canvas = document.getElementById('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.overlay = document.getElementById('overlay');
        this.gallery = document.getElementById('gallery');
        
        this.currentFilter = 'none';
        this.currentFrame = 'none';
        this.stream = null;
        this.photos = [];
        
        this.initializeEventListeners();
        this.loadPhotosFromStorage();
    }

    initializeEventListeners() {
        // Camera controls
        document.getElementById('startCamera').addEventListener('click', () => this.startCamera());
        document.getElementById('captureBtn').addEventListener('click', () => this.capturePhoto());
        document.getElementById('downloadBtn').addEventListener('click', () => this.downloadPhoto());
        document.getElementById('clearGallery').addEventListener('click', () => this.clearGallery());

        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.setFilter(e.target.dataset.filter, e.target));
        });

        // Frame buttons
        document.querySelectorAll('.frame-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.setFrame(e.target.dataset.frame, e.target));
        });
    }

    async startCamera() {
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({ 
                video: { 
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                } 
            });
            
            this.video.srcObject = this.stream;
            
            this.video.onloadedmetadata = () => {
                this.canvas.width = this.video.videoWidth;
                this.canvas.height = this.video.videoHeight;
                
                document.getElementById('startCamera').disabled = true;
                document.getElementById('captureBtn').disabled = false;
                
                this.showNotification('Camera đã được bật thành công!', 'success');
            };
            
        } catch (error) {
            console.error('Lỗi khi truy cập camera:', error);
            this.showNotification('Không thể truy cập camera. Vui lòng kiểm tra quyền truy cập.', 'error');
        }
    }

    setFilter(filter, button) {
        // Remove active class from all filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // Remove all filter classes
        this.video.className = '';
        this.overlay.className = 'overlay';
        
        // Add new filter class
        if (filter !== 'none') {
            this.video.classList.add(`filter-${filter}`);
        }
        
        this.currentFilter = filter;
    }

    setFrame(frame, button) {
        // Remove active class from all frame buttons
        document.querySelectorAll('.frame-btn').forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // Remove all frame classes
        this.overlay.className = 'overlay';
        
        // Add new frame class
        if (frame !== 'none') {
            this.overlay.classList.add(`frame-${frame}`);
        }
        
        this.currentFrame = frame;
    }

    capturePhoto() {
        if (!this.stream) {
            this.showNotification('Vui lòng bật camera trước!', 'error');
            return;
        }

        // Draw video frame to canvas
        this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
        
        // Apply filter to canvas
        this.applyCanvasFilter();
        
        // Apply frame to canvas
        this.applyCanvasFrame();
        
        // Get image data
        const imageData = this.canvas.toDataURL('image/png');
        
        // Add to photos array
        const photo = {
            id: Date.now(),
            data: imageData,
            timestamp: new Date().toLocaleString('vi-VN'),
            filter: this.currentFilter,
            frame: this.currentFrame
        };
        
        this.photos.unshift(photo);
        this.savePhotosToStorage();
        this.renderGallery();
        
        // Enable download button
        document.getElementById('downloadBtn').disabled = false;
        
        this.showNotification('Ảnh đã được chụp thành công!', 'success');
        
        // Add capture effect
        this.addCaptureEffect();
    }

    applyCanvasFilter() {
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const data = imageData.data;
        
        switch (this.currentFilter) {
            case 'sepia':
                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];
                    
                    data[i] = Math.min(255, (r * 0.393) + (g * 0.769) + (b * 0.189));
                    data[i + 1] = Math.min(255, (r * 0.349) + (g * 0.686) + (b * 0.168));
                    data[i + 2] = Math.min(255, (r * 0.272) + (g * 0.534) + (b * 0.131));
                }
                break;
                
            case 'grayscale':
                for (let i = 0; i < data.length; i += 4) {
                    const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
                    data[i] = gray;
                    data[i + 1] = gray;
                    data[i + 2] = gray;
                }
                break;
                
            case 'brightness':
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.min(255, data[i] * 1.5);
                    data[i + 1] = Math.min(255, data[i + 1] * 1.5);
                    data[i + 2] = Math.min(255, data[i + 2] * 1.5);
                }
                break;
                
            case 'contrast':
                const factor = 1.5;
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.min(255, Math.max(0, factor * (data[i] - 128) + 128));
                    data[i + 1] = Math.min(255, Math.max(0, factor * (data[i + 1] - 128) + 128));
                    data[i + 2] = Math.min(255, Math.max(0, factor * (data[i + 2] - 128) + 128));
                }
                break;
        }
        
        this.ctx.putImageData(imageData, 0, 0);
    }

    applyCanvasFrame() {
        if (this.currentFrame === 'none') return;
        
        const frameWidth = 20;
        this.ctx.strokeStyle = this.getFrameColor();
        this.ctx.lineWidth = frameWidth;
        this.ctx.strokeRect(frameWidth/2, frameWidth/2, 
                           this.canvas.width - frameWidth, 
                           this.canvas.height - frameWidth);
    }

    getFrameColor() {
        switch (this.currentFrame) {
            case 'classic': return '#8B4513';
            case 'modern': return '#333';
            case 'fun': return '#FF69B4';
            case 'vintage': return '#D2691E';
            default: return '#000';
        }
    }

    addCaptureEffect() {
        const flash = document.createElement('div');
        flash.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            opacity: 0.8;
            z-index: 9999;
            pointer-events: none;
        `;
        
        document.body.appendChild(flash);
        
        setTimeout(() => {
            flash.style.opacity = '0';
            flash.style.transition = 'opacity 0.3s ease';
            setTimeout(() => document.body.removeChild(flash), 300);
        }, 100);
    }

    downloadPhoto() {
        if (this.photos.length === 0) {
            this.showNotification('Không có ảnh nào để tải xuống!', 'error');
            return;
        }
        
        const latestPhoto = this.photos[0];
        const link = document.createElement('a');
        link.download = `photobooth_${latestPhoto.id}.png`;
        link.href = latestPhoto.data;
        link.click();
        
        this.showNotification('Ảnh đã được tải xuống!', 'success');
    }

    renderGallery() {
        this.gallery.innerHTML = '';
        
        this.photos.forEach(photo => {
            const item = document.createElement('div');
            item.className = 'gallery-item';
            item.innerHTML = `
                <img src="${photo.data}" alt="Photo ${photo.id}" title="${photo.timestamp}">
                <button class="delete-btn" onclick="photobooth.deletePhoto(${photo.id})">×</button>
            `;
            
            // Add click to download
            item.querySelector('img').addEventListener('click', () => {
                const link = document.createElement('a');
                link.download = `photobooth_${photo.id}.png`;
                link.href = photo.data;
                link.click();
            });
            
            this.gallery.appendChild(item);
        });
    }

    deletePhoto(photoId) {
        this.photos = this.photos.filter(photo => photo.id !== photoId);
        this.savePhotosToStorage();
        this.renderGallery();
        this.showNotification('Ảnh đã được xóa!', 'success');
    }

    clearGallery() {
        if (this.photos.length === 0) {
            this.showNotification('Thư viện ảnh đã trống!', 'info');
            return;
        }
        
        if (confirm('Bạn có chắc chắn muốn xóa tất cả ảnh?')) {
            this.photos = [];
            this.savePhotosToStorage();
            this.renderGallery();
            this.showNotification('Đã xóa tất cả ảnh!', 'success');
        }
    }

    savePhotosToStorage() {
        try {
            localStorage.setItem('photobooth_photos', JSON.stringify(this.photos));
        } catch (error) {
            console.error('Lỗi khi lưu ảnh:', error);
        }
    }

    loadPhotosFromStorage() {
        try {
            const saved = localStorage.getItem('photobooth_photos');
            if (saved) {
                this.photos = JSON.parse(saved);
                this.renderGallery();
            }
        } catch (error) {
            console.error('Lỗi khi tải ảnh:', error);
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        switch (type) {
            case 'success':
                notification.style.background = '#4CAF50';
                break;
            case 'error':
                notification.style.background = '#f44336';
                break;
            case 'info':
                notification.style.background = '#2196F3';
                break;
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transition = 'opacity 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize photobooth when page loads
let photobooth;
document.addEventListener('DOMContentLoaded', () => {
    photobooth = new Photobooth();
});
