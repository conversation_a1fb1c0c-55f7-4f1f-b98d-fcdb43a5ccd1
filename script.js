class Photobooth {
    constructor() {
        this.video = document.getElementById('video');
        this.canvas = document.getElementById('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.overlay = document.getElementById('overlay');
        this.gallery = document.getElementById('gallery');
        
        this.currentFilter = 'none';
        this.currentFrame = 'none';
        this.stream = null;
        this.photos = [];
        this.overlayElements = [];
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.isRecording = false;
        this.selectedMedia = null;
        this.beautySettings = {
            skinSmooth: 0,
            skinBrighten: 0,
            skinTone: 0,
            blemishRemoval: 0
        };
        
        this.initializeEventListeners();
        this.loadPhotosFromStorage();
    }

    initializeEventListeners() {
        // Camera controls
        document.getElementById('startCamera').addEventListener('click', () => this.startCamera());
        document.getElementById('captureBtn').addEventListener('click', () => this.capturePhoto());
        document.getElementById('burstBtn').addEventListener('click', () => this.burstCapture());
        document.getElementById('recordBtn').addEventListener('click', () => this.toggleRecording());
        document.getElementById('downloadBtn').addEventListener('click', () => this.downloadMedia());
        document.getElementById('clearGallery').addEventListener('click', () => this.clearGallery());
        document.getElementById('shareBtn').addEventListener('click', () => this.openShareModal());

        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.setFilter(e.target.dataset.filter, e.target));
        });

        // Frame buttons
        document.querySelectorAll('.frame-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.setFrame(e.target.dataset.frame, e.target));
        });

        // Sticker buttons
        document.querySelectorAll('.sticker-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.addSticker(e.target.dataset.sticker));
        });

        // Text controls
        document.getElementById('addText').addEventListener('click', () => this.addText());
        document.getElementById('clearText').addEventListener('click', () => this.clearText());
        document.getElementById('clearStickers').addEventListener('click', () => this.clearStickers());

        // Share modal
        document.querySelector('.close').addEventListener('click', () => this.closeShareModal());
        document.querySelectorAll('.share-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.shareMedia(e.target.dataset.platform));
        });

        // Click outside modal to close
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('shareModal');
            if (e.target === modal) {
                this.closeShareModal();
            }
        });

        // Beauty filter controls
        this.initBeautyControls();
    }

    async startCamera() {
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                },
                audio: true // Thêm audio cho video recording
            });

            this.video.srcObject = this.stream;

            this.video.onloadedmetadata = () => {
                this.canvas.width = this.video.videoWidth;
                this.canvas.height = this.video.videoHeight;

                document.getElementById('startCamera').disabled = true;
                document.getElementById('captureBtn').disabled = false;
                document.getElementById('burstBtn').disabled = false;
                document.getElementById('recordBtn').disabled = false;

                this.showNotification('Camera đã được bật thành công!', 'success');
            };

        } catch (error) {
            console.error('Lỗi khi truy cập camera:', error);
            this.showNotification('Không thể truy cập camera. Vui lòng kiểm tra quyền truy cập.', 'error');
        }
    }

    setFilter(filter, button) {
        // Remove active class from all filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        this.currentFilter = filter;

        // Reapply beauty filters which will handle the base filter too
        this.applyBeautyFilters();
    }

    setFrame(frame, button) {
        // Remove active class from all frame buttons
        document.querySelectorAll('.frame-btn').forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // Remove all frame classes
        this.overlay.className = 'overlay';
        
        // Add new frame class
        if (frame !== 'none') {
            this.overlay.classList.add(`frame-${frame}`);
        }
        
        this.currentFrame = frame;
    }

    addSticker(sticker) {
        const stickerElement = document.createElement('div');
        stickerElement.className = 'overlay-element overlay-sticker';
        stickerElement.textContent = sticker;
        stickerElement.style.left = '50%';
        stickerElement.style.top = '50%';
        stickerElement.style.transform = 'translate(-50%, -50%)';

        // Add delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-overlay';
        deleteBtn.textContent = '×';
        deleteBtn.onclick = () => this.removeOverlayElement(stickerElement);
        stickerElement.appendChild(deleteBtn);

        this.overlay.appendChild(stickerElement);
        this.overlayElements.push(stickerElement);

        // Make draggable
        this.makeDraggable(stickerElement);

        this.showNotification(`Đã thêm sticker ${sticker}!`, 'success');
    }

    addText() {
        const textInput = document.getElementById('textInput');
        const textColor = document.getElementById('textColor').value;
        const textSize = document.getElementById('textSize').value;

        if (!textInput.value.trim()) {
            this.showNotification('Vui lòng nhập text!', 'error');
            return;
        }

        const textElement = document.createElement('div');
        textElement.className = 'overlay-element overlay-text';
        textElement.textContent = textInput.value;
        textElement.style.color = textColor;
        textElement.style.fontSize = textSize + 'px';
        textElement.style.left = '50%';
        textElement.style.top = '30%';
        textElement.style.transform = 'translate(-50%, -50%)';

        // Add delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-overlay';
        deleteBtn.textContent = '×';
        deleteBtn.onclick = () => this.removeOverlayElement(textElement);
        textElement.appendChild(deleteBtn);

        this.overlay.appendChild(textElement);
        this.overlayElements.push(textElement);

        // Make draggable
        this.makeDraggable(textElement);

        textInput.value = '';
        this.showNotification('Đã thêm text!', 'success');
    }

    makeDraggable(element) {
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        element.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('delete-overlay')) return;

            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;

            const rect = element.getBoundingClientRect();
            const parentRect = this.overlay.getBoundingClientRect();
            startLeft = rect.left - parentRect.left;
            startTop = rect.top - parentRect.top;

            element.style.transform = 'none';
            element.style.left = startLeft + 'px';
            element.style.top = startTop + 'px';
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            element.style.left = (startLeft + deltaX) + 'px';
            element.style.top = (startTop + deltaY) + 'px';
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });
    }

    removeOverlayElement(element) {
        element.remove();
        this.overlayElements = this.overlayElements.filter(el => el !== element);
    }

    clearStickers() {
        this.overlayElements.filter(el => el.classList.contains('overlay-sticker')).forEach(el => {
            el.remove();
        });
        this.overlayElements = this.overlayElements.filter(el => !el.classList.contains('overlay-sticker'));
        this.showNotification('Đã xóa tất cả stickers!', 'success');
    }

    clearText() {
        this.overlayElements.filter(el => el.classList.contains('overlay-text')).forEach(el => {
            el.remove();
        });
        this.overlayElements = this.overlayElements.filter(el => !el.classList.contains('overlay-text'));
        this.showNotification('Đã xóa tất cả text!', 'success');
    }

    initBeautyControls() {
        // Initialize beauty sliders
        const sliders = ['skinSmooth', 'skinBrighten', 'skinTone', 'blemishRemoval'];

        sliders.forEach(sliderId => {
            const slider = document.getElementById(sliderId);
            const valueDisplay = slider.nextElementSibling;

            slider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                this.beautySettings[sliderId] = value;
                valueDisplay.textContent = value + '%';
                this.applyBeautyFilters();
            });
        });

        // Reset button
        document.getElementById('resetBeauty').addEventListener('click', () => {
            this.resetBeautyFilters();
        });

        // Auto beauty button
        document.getElementById('autoBeauty').addEventListener('click', () => {
            this.applyAutoBeauty();
        });

        // Max beauty button
        document.getElementById('maxBeauty').addEventListener('click', () => {
            this.applyMaxBeauty();
        });
    }

    applyBeautyFilters() {
        // Remove existing beauty classes
        this.video.className = this.video.className.replace(/beauty-\w+-\w+/g, '').trim();

        // Apply current filter if any
        if (this.currentFilter !== 'none') {
            this.video.classList.add(`filter-${this.currentFilter}`);
        }

        // Build beauty filter CSS
        let filterCSS = '';

        // Enhanced skin smoothing (stronger blur + brightness)
        if (this.beautySettings.skinSmooth > 0) {
            const blurAmount = (this.beautySettings.skinSmooth / 100) * 2.5;
            const brightnessAmount = 1 + (this.beautySettings.skinSmooth / 100) * 0.25;
            filterCSS += `blur(${blurAmount}px) brightness(${brightnessAmount}) `;
        }

        // Enhanced skin brightening (stronger brightness + contrast)
        if (this.beautySettings.skinBrighten > 0) {
            const brightnessAmount = 1 + (this.beautySettings.skinBrighten / 100) * 0.5;
            const contrastAmount = 1 - (this.beautySettings.skinBrighten / 100) * 0.25;
            filterCSS += `brightness(${brightnessAmount}) contrast(${contrastAmount}) `;
        }

        // Enhanced skin tone (stronger hue + saturation + brightness)
        if (this.beautySettings.skinTone > 0) {
            const hueAmount = -(this.beautySettings.skinTone / 100) * 25;
            const saturationAmount = 1 + (this.beautySettings.skinTone / 100) * 0.5;
            const brightnessAmount = 1 + (this.beautySettings.skinTone / 100) * 0.25;
            filterCSS += `hue-rotate(${hueAmount}deg) saturate(${saturationAmount}) brightness(${brightnessAmount}) `;
        }

        // Enhanced blemish removal (stronger blur + contrast)
        if (this.beautySettings.blemishRemoval > 0) {
            const blurAmount = (this.beautySettings.blemishRemoval / 100) * 1.8;
            const contrastAmount = 1 + (this.beautySettings.blemishRemoval / 100) * 0.25;
            filterCSS += `blur(${blurAmount}px) contrast(${contrastAmount}) `;
        }

        // Apply combined filter
        if (filterCSS.trim()) {
            this.video.style.filter = filterCSS.trim();
        } else if (this.currentFilter !== 'none') {
            // Keep original filter if no beauty filters
            this.video.style.filter = '';
        } else {
            this.video.style.filter = 'none';
        }
    }

    resetBeautyFilters() {
        // Reset all beauty settings
        Object.keys(this.beautySettings).forEach(key => {
            this.beautySettings[key] = 0;
            const slider = document.getElementById(key);
            const valueDisplay = slider.nextElementSibling;
            slider.value = 0;
            valueDisplay.textContent = '0%';
        });

        this.applyBeautyFilters();
        this.showNotification('Đã reset tất cả beauty filters!', 'success');
    }

    applyAutoBeauty() {
        // Apply stronger beauty settings for better results
        const autoSettings = {
            skinSmooth: 50,
            skinBrighten: 35,
            skinTone: 40,
            blemishRemoval: 30
        };

        Object.keys(autoSettings).forEach(key => {
            this.beautySettings[key] = autoSettings[key];
            const slider = document.getElementById(key);
            const valueDisplay = slider.nextElementSibling;
            slider.value = autoSettings[key];
            valueDisplay.textContent = autoSettings[key] + '%';
        });

        this.applyBeautyFilters();
        this.showNotification('Đã áp dụng beauty filter tự động!', 'success');
    }

    applyMaxBeauty() {
        // Apply maximum beauty settings for dramatic improvement
        const maxSettings = {
            skinSmooth: 75,
            skinBrighten: 60,
            skinTone: 65,
            blemishRemoval: 50
        };

        Object.keys(maxSettings).forEach(key => {
            this.beautySettings[key] = maxSettings[key];
            const slider = document.getElementById(key);
            const valueDisplay = slider.nextElementSibling;
            slider.value = maxSettings[key];
            valueDisplay.textContent = maxSettings[key] + '%';
        });

        this.applyBeautyFilters();
        this.showNotification('Đã áp dụng beauty filter tối đa! 🌟', 'success');
    }

    capturePhoto() {
        if (!this.stream) {
            this.showNotification('Vui lòng bật camera trước!', 'error');
            return;
        }

        // Draw video frame to canvas
        this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
        
        // Apply filter to canvas
        this.applyCanvasFilter();

        // Apply beauty filters to canvas
        this.applyCanvasBeautyFilters();

        // Apply frame to canvas
        this.applyCanvasFrame();

        // Apply overlay elements to canvas
        this.applyOverlayToCanvas();

        // Get image data
        const imageData = this.canvas.toDataURL('image/png');

        // Add to photos array
        const photo = {
            id: Date.now(),
            data: imageData,
            timestamp: new Date().toLocaleString('vi-VN'),
            filter: this.currentFilter,
            frame: this.currentFrame,
            type: 'photo',
            beautySettings: { ...this.beautySettings }
        };
        
        this.photos.unshift(photo);
        this.savePhotosToStorage();
        this.renderGallery();
        
        // Enable download button
        document.getElementById('downloadBtn').disabled = false;
        
        this.showNotification('Ảnh đã được chụp thành công!', 'success');
        
        // Add capture effect
        this.addCaptureEffect();
    }

    async burstCapture() {
        if (!this.stream) {
            this.showNotification('Vui lòng bật camera trước!', 'error');
            return;
        }

        this.showNotification('Chụp liên tiếp sẽ bắt đầu sau 3 giây!', 'info');

        // Disable buttons during burst
        document.getElementById('burstBtn').disabled = true;
        document.getElementById('captureBtn').disabled = true;

        // Countdown
        for (let i = 3; i > 0; i--) {
            this.showCountdown(i);
            await this.sleep(1000);
        }

        // Take 5 photos with 1 second interval
        for (let i = 0; i < 5; i++) {
            this.capturePhoto();
            if (i < 4) await this.sleep(1000);
        }

        // Re-enable buttons
        document.getElementById('burstBtn').disabled = false;
        document.getElementById('captureBtn').disabled = false;

        this.showNotification('Đã chụp 5 ảnh liên tiếp!', 'success');
    }

    showCountdown(number) {
        const countdown = document.createElement('div');
        countdown.className = 'burst-countdown';
        countdown.textContent = number;
        this.overlay.appendChild(countdown);

        setTimeout(() => {
            if (countdown.parentNode) {
                countdown.remove();
            }
        }, 900);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    async startRecording() {
        if (!this.stream) {
            this.showNotification('Vui lòng bật camera trước!', 'error');
            return;
        }

        try {
            this.recordedChunks = [];
            this.mediaRecorder = new MediaRecorder(this.stream, {
                mimeType: 'video/webm;codecs=vp9'
            });

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.saveRecording();
            };

            this.mediaRecorder.start();
            this.isRecording = true;

            // Update UI
            document.getElementById('recordBtn').textContent = '⏹️ Dừng quay';
            document.getElementById('recordBtn').classList.remove('btn-warning');
            document.getElementById('recordBtn').classList.add('btn-danger');

            // Add recording indicator
            const indicator = document.createElement('div');
            indicator.className = 'recording-indicator';
            indicator.textContent = '🔴 ĐANG QUAY';
            indicator.id = 'recordingIndicator';
            this.overlay.appendChild(indicator);

            this.showNotification('Bắt đầu quay video!', 'success');

        } catch (error) {
            console.error('Lỗi khi bắt đầu quay:', error);
            this.showNotification('Không thể bắt đầu quay video!', 'error');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;

            // Update UI
            document.getElementById('recordBtn').textContent = '🎬 Quay video';
            document.getElementById('recordBtn').classList.remove('btn-danger');
            document.getElementById('recordBtn').classList.add('btn-warning');

            // Remove recording indicator
            const indicator = document.getElementById('recordingIndicator');
            if (indicator) {
                indicator.remove();
            }

            this.showNotification('Đã dừng quay video!', 'success');
        }
    }

    saveRecording() {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const videoUrl = URL.createObjectURL(blob);

        const video = {
            id: Date.now(),
            data: videoUrl,
            blob: blob,
            timestamp: new Date().toLocaleString('vi-VN'),
            type: 'video',
            filter: this.currentFilter,
            frame: this.currentFrame
        };

        this.photos.unshift(video);
        this.savePhotosToStorage();
        this.renderGallery();

        document.getElementById('downloadBtn').disabled = false;
        this.showNotification('Video đã được lưu!', 'success');
    }

    applyCanvasFilter() {
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const data = imageData.data;
        
        switch (this.currentFilter) {
            case 'sepia':
                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];
                    
                    data[i] = Math.min(255, (r * 0.393) + (g * 0.769) + (b * 0.189));
                    data[i + 1] = Math.min(255, (r * 0.349) + (g * 0.686) + (b * 0.168));
                    data[i + 2] = Math.min(255, (r * 0.272) + (g * 0.534) + (b * 0.131));
                }
                break;
                
            case 'grayscale':
                for (let i = 0; i < data.length; i += 4) {
                    const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
                    data[i] = gray;
                    data[i + 1] = gray;
                    data[i + 2] = gray;
                }
                break;
                
            case 'brightness':
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.min(255, data[i] * 1.5);
                    data[i + 1] = Math.min(255, data[i + 1] * 1.5);
                    data[i + 2] = Math.min(255, data[i + 2] * 1.5);
                }
                break;
                
            case 'contrast':
                const factor = 1.5;
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.min(255, Math.max(0, factor * (data[i] - 128) + 128));
                    data[i + 1] = Math.min(255, Math.max(0, factor * (data[i + 1] - 128) + 128));
                    data[i + 2] = Math.min(255, Math.max(0, factor * (data[i + 2] - 128) + 128));
                }
                break;
        }
        
        this.ctx.putImageData(imageData, 0, 0);
    }

    applyCanvasBeautyFilters() {
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const data = imageData.data;

        // Apply beauty filters to canvas with stronger effects
        for (let i = 0; i < data.length; i += 4) {
            let r = data[i];
            let g = data[i + 1];
            let b = data[i + 2];

            // Enhanced skin smoothing - more aggressive smoothing
            if (this.beautySettings.skinSmooth > 0) {
                const smoothFactor = this.beautySettings.skinSmooth / 100;

                // Better skin tone detection (expanded range)
                if ((r > 80 && g > 60 && b > 40) && (r >= g && g >= b)) {
                    // More aggressive smoothing
                    const avgR = (r + g + b) / 3;
                    r = r + (avgR - r) * smoothFactor * 0.6;
                    g = g + (avgR - g) * smoothFactor * 0.5;
                    b = b + (avgR - b) * smoothFactor * 0.4;

                    // Additional noise reduction
                    const noise = Math.random() * 10 - 5;
                    r = Math.max(0, Math.min(255, r - noise * smoothFactor));
                    g = Math.max(0, Math.min(255, g - noise * smoothFactor));
                    b = Math.max(0, Math.min(255, b - noise * smoothFactor));
                }
            }

            // Enhanced skin brightening
            if (this.beautySettings.skinBrighten > 0) {
                const brightenFactor = 1 + (this.beautySettings.skinBrighten / 100) * 0.5;
                const contrastFactor = 1 - (this.beautySettings.skinBrighten / 100) * 0.25;

                // More aggressive brightening for skin tones
                if ((r > 80 && g > 60 && b > 40) && (r >= g && g >= b)) {
                    r = Math.min(255, Math.max(0, (r - 128) * contrastFactor + 128) * brightenFactor);
                    g = Math.min(255, Math.max(0, (g - 128) * contrastFactor + 128) * brightenFactor);
                    b = Math.min(255, Math.max(0, (b - 128) * contrastFactor + 128) * brightenFactor);
                }
            }

            // Enhanced skin tone (stronger pink/warm tones)
            if (this.beautySettings.skinTone > 0) {
                const toneFactor = this.beautySettings.skinTone / 100;

                // More liberal skin tone detection
                if ((r > 80 && g > 60 && b > 40) && (r >= g && g >= b)) {
                    // Add stronger warm pink tones
                    r = Math.min(255, r + toneFactor * 35);
                    g = Math.min(255, g + toneFactor * 20);
                    b = Math.min(255, b + toneFactor * 10);

                    // Increase saturation more aggressively
                    const max = Math.max(r, g, b);
                    const min = Math.min(r, g, b);
                    const saturation = (max - min) / max;
                    const newSaturation = Math.min(1, saturation + toneFactor * 0.4);

                    if (max > 0) {
                        const factor = newSaturation / saturation || 1;
                        r = min + (r - min) * factor;
                        g = min + (g - min) * factor;
                        b = min + (b - min) * factor;
                    }

                    // Add subtle glow effect
                    const glowFactor = toneFactor * 0.1;
                    r = Math.min(255, r + glowFactor * 20);
                    g = Math.min(255, g + glowFactor * 15);
                    b = Math.min(255, b + glowFactor * 10);
                }
            }

            // Enhanced blemish removal - more aggressive smoothing
            if (this.beautySettings.blemishRemoval > 0) {
                const removalFactor = this.beautySettings.blemishRemoval / 100;

                // Detect high contrast areas (potential blemishes) - lower threshold
                const brightness = (r + g + b) / 3;
                const contrast = Math.max(Math.abs(r - brightness), Math.abs(g - brightness), Math.abs(b - brightness));

                if (contrast > 15) { // Lower threshold for more coverage
                    // More aggressive contrast reduction
                    r = r + (brightness - r) * removalFactor * 0.8;
                    g = g + (brightness - g) * removalFactor * 0.8;
                    b = b + (brightness - b) * removalFactor * 0.8;

                    // Additional smoothing for skin areas
                    if ((r > 80 && g > 60 && b > 40) && (r >= g && g >= b)) {
                        const avgColor = (r + g + b) / 3;
                        r = r + (avgColor - r) * removalFactor * 0.3;
                        g = g + (avgColor - g) * removalFactor * 0.3;
                        b = b + (avgColor - b) * removalFactor * 0.3;
                    }
                }
            }

            // Clamp values
            data[i] = Math.min(255, Math.max(0, r));
            data[i + 1] = Math.min(255, Math.max(0, g));
            data[i + 2] = Math.min(255, Math.max(0, b));
        }

        this.ctx.putImageData(imageData, 0, 0);
    }

    applyCanvasFrame() {
        if (this.currentFrame === 'none') return;

        const frameWidth = 30; // Increased frame width for better visibility
        this.ctx.save(); // Save current context state

        // Set frame properties
        this.ctx.strokeStyle = this.getFrameColor();
        this.ctx.lineWidth = frameWidth;
        this.ctx.lineCap = 'square';
        this.ctx.lineJoin = 'miter';

        // Draw frame border
        this.ctx.strokeRect(frameWidth/2, frameWidth/2,
                           this.canvas.width - frameWidth,
                           this.canvas.height - frameWidth);

        // Add frame-specific effects
        switch (this.currentFrame) {
            case 'classic':
                // Add inner shadow for classic frame
                this.ctx.shadowColor = 'rgba(0,0,0,0.5)';
                this.ctx.shadowBlur = 10;
                this.ctx.shadowOffsetX = 5;
                this.ctx.shadowOffsetY = 5;
                this.ctx.strokeRect(frameWidth/2 + 5, frameWidth/2 + 5,
                                   this.canvas.width - frameWidth - 10,
                                   this.canvas.height - frameWidth - 10);
                break;

            case 'vintage':
                // Add multiple borders for vintage effect
                this.ctx.strokeStyle = '#8B4513';
                this.ctx.lineWidth = frameWidth;
                this.ctx.strokeRect(frameWidth/2, frameWidth/2,
                                   this.canvas.width - frameWidth,
                                   this.canvas.height - frameWidth);

                this.ctx.strokeStyle = '#D2691E';
                this.ctx.lineWidth = frameWidth - 10;
                this.ctx.strokeRect(frameWidth/2 + 5, frameWidth/2 + 5,
                                   this.canvas.width - frameWidth - 10,
                                   this.canvas.height - frameWidth - 10);
                break;

            case 'fun':
                // Add rounded corners effect for fun frame
                this.ctx.strokeStyle = '#FF69B4';
                this.ctx.lineWidth = frameWidth;
                this.drawRoundedRect(frameWidth/2, frameWidth/2,
                                    this.canvas.width - frameWidth,
                                    this.canvas.height - frameWidth, 20);
                break;
        }

        this.ctx.restore(); // Restore context state
    }

    drawRoundedRect(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
        this.ctx.stroke();
    }

    getFrameColor() {
        switch (this.currentFrame) {
            case 'classic': return '#8B4513';
            case 'modern': return '#333';
            case 'fun': return '#FF69B4';
            case 'vintage': return '#D2691E';
            default: return '#000';
        }
    }

    applyOverlayToCanvas() {
        // Apply stickers and text to canvas
        this.overlayElements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const overlayRect = this.overlay.getBoundingClientRect();

            // Calculate relative position
            const x = (rect.left - overlayRect.left) * (this.canvas.width / overlayRect.width);
            const y = (rect.top - overlayRect.top) * (this.canvas.height / overlayRect.height);

            if (element.classList.contains('overlay-sticker')) {
                this.ctx.font = '40px Arial';
                this.ctx.fillText(element.textContent.replace('×', ''), x, y + 30);
            } else if (element.classList.contains('overlay-text')) {
                const fontSize = parseInt(element.style.fontSize) || 30;
                const color = element.style.color || 'white';

                this.ctx.font = `bold ${fontSize}px Arial`;
                this.ctx.fillStyle = color;
                this.ctx.strokeStyle = 'black';
                this.ctx.lineWidth = 2;

                const text = element.textContent.replace('×', '');
                this.ctx.strokeText(text, x, y + fontSize);
                this.ctx.fillText(text, x, y + fontSize);
            }
        });
    }

    addCaptureEffect() {
        const flash = document.createElement('div');
        flash.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            opacity: 0.8;
            z-index: 9999;
            pointer-events: none;
        `;
        
        document.body.appendChild(flash);
        
        setTimeout(() => {
            flash.style.opacity = '0';
            flash.style.transition = 'opacity 0.3s ease';
            setTimeout(() => document.body.removeChild(flash), 300);
        }, 100);
    }

    downloadMedia() {
        if (this.photos.length === 0) {
            this.showNotification('Không có ảnh/video nào để tải xuống!', 'error');
            return;
        }

        const latestMedia = this.photos[0];
        const link = document.createElement('a');

        if (latestMedia.type === 'video') {
            link.download = `photobooth_video_${latestMedia.id}.webm`;
            link.href = latestMedia.data;
        } else {
            link.download = `photobooth_${latestMedia.id}.png`;
            link.href = latestMedia.data;
        }

        link.click();
        this.showNotification(`${latestMedia.type === 'video' ? 'Video' : 'Ảnh'} đã được tải xuống!`, 'success');
    }

    renderGallery() {
        this.gallery.innerHTML = '';

        this.photos.forEach(media => {
            const item = document.createElement('div');
            item.className = 'gallery-item';

            if (media.type === 'video') {
                item.innerHTML = `
                    <video src="${media.data}" title="${media.timestamp}"></video>
                    <div class="media-type">VIDEO</div>
                    <button class="delete-btn" onclick="photobooth.deleteMedia(${media.id})">×</button>
                `;

                // Add click to download video
                item.querySelector('video').addEventListener('click', () => {
                    this.selectedMedia = media;
                    document.getElementById('shareBtn').disabled = false;
                    const link = document.createElement('a');
                    link.download = `photobooth_video_${media.id}.webm`;
                    link.href = media.data;
                    link.click();
                });
            } else {
                item.innerHTML = `
                    <img src="${media.data}" alt="Photo ${media.id}" title="${media.timestamp}">
                    <div class="media-type">PHOTO</div>
                    <button class="delete-btn" onclick="photobooth.deleteMedia(${media.id})">×</button>
                `;

                // Add click to download photo
                item.querySelector('img').addEventListener('click', () => {
                    this.selectedMedia = media;
                    document.getElementById('shareBtn').disabled = false;
                    const link = document.createElement('a');
                    link.download = `photobooth_${media.id}.png`;
                    link.href = media.data;
                    link.click();
                });
            }

            this.gallery.appendChild(item);
        });

        // Enable share button if there are media
        if (this.photos.length > 0) {
            this.selectedMedia = this.photos[0];
            document.getElementById('shareBtn').disabled = false;
        }
    }

    deleteMedia(mediaId) {
        this.photos = this.photos.filter(media => media.id !== mediaId);
        this.savePhotosToStorage();
        this.renderGallery();
        this.showNotification('Media đã được xóa!', 'success');
    }

    clearGallery() {
        if (this.photos.length === 0) {
            this.showNotification('Thư viện ảnh đã trống!', 'info');
            return;
        }
        
        if (confirm('Bạn có chắc chắn muốn xóa tất cả ảnh/video?')) {
            this.photos = [];
            this.savePhotosToStorage();
            this.renderGallery();
            document.getElementById('shareBtn').disabled = true;
            this.showNotification('Đã xóa tất cả media!', 'success');
        }
    }

    savePhotosToStorage() {
        try {
            // Only save photos (not videos) to localStorage due to size limitations
            const photosToSave = this.photos.filter(media => media.type !== 'video').map(photo => ({
                id: photo.id,
                data: photo.data,
                timestamp: photo.timestamp,
                filter: photo.filter,
                frame: photo.frame,
                type: photo.type || 'photo'
            }));
            localStorage.setItem('photobooth_photos', JSON.stringify(photosToSave));
        } catch (error) {
            console.error('Lỗi khi lưu ảnh:', error);
        }
    }

    loadPhotosFromStorage() {
        try {
            const saved = localStorage.getItem('photobooth_photos');
            if (saved) {
                const savedPhotos = JSON.parse(saved);
                // Only load photos, videos are session-only
                this.photos = savedPhotos.filter(media => media.type !== 'video');
                this.renderGallery();
            }
        } catch (error) {
            console.error('Lỗi khi tải ảnh:', error);
        }
    }

    openShareModal() {
        if (!this.selectedMedia) {
            this.showNotification('Vui lòng chọn ảnh/video để chia sẻ!', 'error');
            return;
        }
        document.getElementById('shareModal').style.display = 'block';
    }

    closeShareModal() {
        document.getElementById('shareModal').style.display = 'none';
    }

    async shareMedia(platform) {
        if (!this.selectedMedia) {
            this.showNotification('Không có media nào được chọn!', 'error');
            return;
        }

        const mediaType = this.selectedMedia.type === 'video' ? 'video' : 'ảnh';
        const shareText = `Xem ${mediaType} tuyệt vời này từ Photobooth! 📸`;

        try {
            switch (platform) {
                case 'facebook':
                    if (navigator.share) {
                        await this.nativeShare();
                    } else {
                        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}&quote=${encodeURIComponent(shareText)}`, '_blank');
                    }
                    break;

                case 'twitter':
                    window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(window.location.href)}`, '_blank');
                    break;

                case 'instagram':
                    this.showNotification('Vui lòng tải xuống và chia sẻ thủ công trên Instagram!', 'info');
                    this.downloadSelectedMedia();
                    break;

                case 'whatsapp':
                    window.open(`https://wa.me/?text=${encodeURIComponent(shareText + ' ' + window.location.href)}`, '_blank');
                    break;

                case 'email':
                    window.open(`mailto:?subject=${encodeURIComponent('Chia sẻ từ Photobooth')}&body=${encodeURIComponent(shareText + '\n\n' + window.location.href)}`, '_blank');
                    break;

                case 'copy':
                    await this.copyToClipboard();
                    break;
            }

            this.closeShareModal();
            this.showNotification(`Đã chia sẻ lên ${platform}!`, 'success');

        } catch (error) {
            console.error('Lỗi khi chia sẻ:', error);
            this.showNotification('Có lỗi khi chia sẻ!', 'error');
        }
    }

    async nativeShare() {
        if (navigator.share) {
            try {
                if (this.selectedMedia.type === 'video') {
                    // For video, we need to create a File object
                    const file = new File([this.selectedMedia.blob], `photobooth_video_${this.selectedMedia.id}.webm`, { type: 'video/webm' });
                    await navigator.share({
                        title: 'Photobooth Video',
                        text: 'Xem video tuyệt vời này từ Photobooth!',
                        files: [file]
                    });
                } else {
                    // For images, convert data URL to blob
                    const response = await fetch(this.selectedMedia.data);
                    const blob = await response.blob();
                    const file = new File([blob], `photobooth_${this.selectedMedia.id}.png`, { type: 'image/png' });
                    await navigator.share({
                        title: 'Photobooth Photo',
                        text: 'Xem ảnh tuyệt vời này từ Photobooth!',
                        files: [file]
                    });
                }
            } catch (error) {
                console.error('Native share failed:', error);
                throw error;
            }
        }
    }

    async copyToClipboard() {
        try {
            if (this.selectedMedia.type === 'video') {
                await navigator.clipboard.writeText('Video từ Photobooth - ' + window.location.href);
            } else {
                // Try to copy image to clipboard
                const response = await fetch(this.selectedMedia.data);
                const blob = await response.blob();
                await navigator.clipboard.write([
                    new ClipboardItem({ [blob.type]: blob })
                ]);
            }
        } catch (error) {
            // Fallback to copying text
            await navigator.clipboard.writeText('Ảnh/Video từ Photobooth - ' + window.location.href);
        }
    }

    downloadSelectedMedia() {
        if (!this.selectedMedia) return;

        const link = document.createElement('a');
        if (this.selectedMedia.type === 'video') {
            link.download = `photobooth_video_${this.selectedMedia.id}.webm`;
        } else {
            link.download = `photobooth_${this.selectedMedia.id}.png`;
        }
        link.href = this.selectedMedia.data;
        link.click();
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        switch (type) {
            case 'success':
                notification.style.background = '#4CAF50';
                break;
            case 'error':
                notification.style.background = '#f44336';
                break;
            case 'info':
                notification.style.background = '#2196F3';
                break;
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transition = 'opacity 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize photobooth when page loads
let photobooth;
document.addEventListener('DOMContentLoaded', () => {
    photobooth = new Photobooth();
});
