<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photobooth - Chụ<PERSON> ảnh vui nhộn</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📸 Photobooth</h1>
            <p>Chụp ảnh với các hiệu ứng thú vị!</p>
        </header>

        <main>
            <div class="camera-section">
                <div class="video-container">
                    <video id="video" autoplay playsinline></video>
                    <canvas id="canvas" style="display: none;"></canvas>
                    <div class="overlay" id="overlay"></div>
                </div>

                <div class="controls">
                    <button id="startCamera" class="btn btn-primary">
                        🎥 Bật Camera
                    </button>
                    <button id="captureBtn" class="btn btn-success" disabled>
                        📷 Chụp ảnh
                    </button>
                    <button id="burstBtn" class="btn btn-info" disabled>
                        📸 Chụp liên tiếp (3s)
                    </button>
                    <button id="recordBtn" class="btn btn-warning" disabled>
                        🎬 Quay video
                    </button>
                    <button id="downloadBtn" class="btn btn-secondary" disabled>
                        💾 Tải xuống
                    </button>
                </div>

                <div class="filters">
                    <h3>Bộ lọc cơ bản:</h3>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="none">Gốc</button>
                        <button class="filter-btn" data-filter="sepia">Sepia</button>
                        <button class="filter-btn" data-filter="grayscale">Đen trắng</button>
                        <button class="filter-btn" data-filter="blur">Mờ</button>
                        <button class="filter-btn" data-filter="brightness">Sáng</button>
                        <button class="filter-btn" data-filter="contrast">Tương phản</button>
                        <button class="filter-btn" data-filter="hue-rotate">Màu sắc</button>
                    </div>
                </div>

                <div class="beauty-filters">
                    <h3>✨ Bộ lọc làm đẹp:</h3>
                    <div class="beauty-controls">
                        <div class="beauty-control">
                            <label for="skinSmooth">🌟 Mịn da:</label>
                            <input type="range" id="skinSmooth" min="0" max="100" value="0" class="beauty-slider">
                            <span class="beauty-value">0%</span>
                        </div>
                        <div class="beauty-control">
                            <label for="skinBrighten">💡 Sáng da:</label>
                            <input type="range" id="skinBrighten" min="0" max="100" value="0" class="beauty-slider">
                            <span class="beauty-value">0%</span>
                        </div>
                        <div class="beauty-control">
                            <label for="skinTone">🌸 Trắng hồng:</label>
                            <input type="range" id="skinTone" min="0" max="100" value="0" class="beauty-slider">
                            <span class="beauty-value">0%</span>
                        </div>
                        <div class="beauty-control">
                            <label for="blemishRemoval">🎯 Loại bỏ mụn:</label>
                            <input type="range" id="blemishRemoval" min="0" max="100" value="0" class="beauty-slider">
                            <span class="beauty-value">0%</span>
                        </div>
                        <button id="resetBeauty" class="btn btn-sm">↺ Reset</button>
                        <button id="autoBeauty" class="btn btn-sm btn-info">✨ Auto</button>
                    </div>
                </div>

                <div class="frames">
                    <h3>Khung ảnh:</h3>
                    <div class="frame-buttons">
                        <button class="frame-btn active" data-frame="none">Không khung</button>
                        <button class="frame-btn" data-frame="classic">Cổ điển</button>
                        <button class="frame-btn" data-frame="modern">Hiện đại</button>
                        <button class="frame-btn" data-frame="fun">Vui nhộn</button>
                        <button class="frame-btn" data-frame="vintage">Vintage</button>
                    </div>
                </div>

                <div class="stickers">
                    <h3>Sticker/Emoji:</h3>
                    <div class="sticker-buttons">
                        <button class="sticker-btn" data-sticker="😀">😀</button>
                        <button class="sticker-btn" data-sticker="😎">😎</button>
                        <button class="sticker-btn" data-sticker="🥳">🥳</button>
                        <button class="sticker-btn" data-sticker="😍">😍</button>
                        <button class="sticker-btn" data-sticker="🤩">🤩</button>
                        <button class="sticker-btn" data-sticker="❤️">❤️</button>
                        <button class="sticker-btn" data-sticker="⭐">⭐</button>
                        <button class="sticker-btn" data-sticker="🎉">🎉</button>
                        <button class="sticker-btn" data-sticker="🔥">🔥</button>
                        <button class="sticker-btn" data-sticker="💯">💯</button>
                    </div>
                    <button id="clearStickers" class="btn btn-sm">Xóa stickers</button>
                </div>

                <div class="text-overlay">
                    <h3>Thêm text:</h3>
                    <input type="text" id="textInput" placeholder="Nhập text..." maxlength="50">
                    <div class="text-controls">
                        <select id="textColor">
                            <option value="white">Trắng</option>
                            <option value="black">Đen</option>
                            <option value="red">Đỏ</option>
                            <option value="blue">Xanh dương</option>
                            <option value="yellow">Vàng</option>
                            <option value="green">Xanh lá</option>
                            <option value="purple">Tím</option>
                        </select>
                        <select id="textSize">
                            <option value="20">Nhỏ</option>
                            <option value="30" selected>Vừa</option>
                            <option value="40">Lớn</option>
                            <option value="50">Rất lớn</option>
                        </select>
                        <button id="addText" class="btn btn-sm">Thêm text</button>
                        <button id="clearText" class="btn btn-sm">Xóa text</button>
                    </div>
                </div>
            </div>

            <div class="gallery-section">
                <h3>📱 Ảnh & Video đã chụp:</h3>
                <div id="gallery" class="gallery">
                    <!-- Ảnh và video sẽ được thêm vào đây -->
                </div>
                <div class="gallery-controls">
                    <button id="clearGallery" class="btn btn-danger">
                        🗑️ Xóa tất cả
                    </button>
                    <button id="shareBtn" class="btn btn-info" disabled>
                        📤 Chia sẻ
                    </button>
                </div>

                <!-- Share Modal -->
                <div id="shareModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <span class="close">&times;</span>
                        <h3>Chia sẻ ảnh/video</h3>
                        <div class="share-options">
                            <button class="share-btn" data-platform="facebook">📘 Facebook</button>
                            <button class="share-btn" data-platform="twitter">🐦 Twitter</button>
                            <button class="share-btn" data-platform="instagram">📷 Instagram</button>
                            <button class="share-btn" data-platform="whatsapp">💬 WhatsApp</button>
                            <button class="share-btn" data-platform="email">📧 Email</button>
                            <button class="share-btn" data-platform="copy">📋 Copy Link</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>© 2024 Photobooth - Tạo những khoảnh khắc đáng nhớ!</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
